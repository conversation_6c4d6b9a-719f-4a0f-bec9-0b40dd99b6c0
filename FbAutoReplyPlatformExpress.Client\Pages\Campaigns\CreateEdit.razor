@page "/campaigns/edit/{Id:guid}"
@page "/campaigns/create"
@using FbAutoReplyPlatformExpress.Services.Dtos
@using FbAutoReplyPlatformExpress.Services
@using Microsoft.AspNetCore.Authorization
@using FbAutoReplyPlatformExpress.Permissions
@using System.ComponentModel.DataAnnotations
@using FbAutoReplyPlatformExpress.Client.Validation
@using FbAutoReplyPlatformExpress.Components
@using Volo.Abp.AspNetCore.Components.Messages
@inject IJSRuntime JSRuntime
@inject IAutoReplyCampaignService CampaignService
@inject IFacebookPostService PostService
@inject NavigationManager NavigationManager
@inject IUiMessageService Message

@attribute [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Default)]

<div class="campaign-create-edit">
    @if (IsLoading)
    {
        <div class="loading-spinner">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <span>Loading campaign details...</span>
        </div>
    }
    else
    {
        <div class="container-fluid">
            <Row>
                <Column ColumnSize="ColumnSize.Is12">
                    <Card>
                        <CardHeader>
                            <h4>@(IsEditMode ? "Edit Campaign" : "Create New Campaign")</h4>
                        </CardHeader>
                    </Card>
                </Column>
            </Row>

            @if (SelectedPost != null)
            {
                <Row Class="mt-3">
                    <Column ColumnSize="ColumnSize.Is12">
                        <Card>
                            <CardHeader>
                                <h5>Selected Post</h5>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                    <Column ColumnSize="ColumnSize.Is3">
                                        @if (!string.IsNullOrEmpty(SelectedPost.PictureUrl))
                                        {
                                            <img src="@SelectedPost.PictureUrl" 
                                                 alt="Post image" 
                                                 class="img-fluid rounded" 
                                                 style="max-height: 150px; width: 100%; object-fit: cover;" />
                                        }
                                    </Column>
                                    <Column ColumnSize="ColumnSize.Is9">
                                        <div class="d-flex align-items-center mb-2">
                                            <strong class="text-primary">@SelectedPost.PageName</strong>
                                            <small class="text-muted ms-2">@SelectedPost.FacebookCreatedTime.ToString("MMM dd, yyyy")</small>
                                        </div>
                                        @if (!string.IsNullOrEmpty(SelectedPost.Message))
                                        {
                                            <p class="mb-2">@SelectedPost.Message</p>
                                        }
                                        @if (!string.IsNullOrEmpty(SelectedPost.PermalinkUrl))
                                        {
                                            <Button Color="Color.Link" Size="Size.Small" @onclick="() => OpenFacebookPostAsync(SelectedPost.PermalinkUrl!)">
                                                <Icon Name="IconName.Link" />
                                                View on Facebook
                                            </Button>
                                        }
                                    </Column>
                                </Row>
                            </CardBody>
                        </Card>
                    </Column>
                </Row>
            }

            <!-- Campaign Form -->
            <Validations @ref="ValidationsRef" Model="@Campaign" ValidateOnLoad="false">
                <Row>
                    <Column ColumnSize="ColumnSize.Is12">
                        <Card>
                            <CardHeader>
                                <h5>Campaign Configuration</h5>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Validation>
                                            <Field>
                                                <FieldLabel>Campaign Name *</FieldLabel>
                                                <TextEdit @bind-Text="@Campaign.CampaignName" Placeholder="Enter campaign name">
                                                    <ValidationError />
                                                </TextEdit>
                                            </Field>
                                        </Validation>
                                    </Column>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <FieldLabel>Max Replies Per User</FieldLabel>
                                            <NumericEdit @bind-Value="@Campaign.MaxRepliesPerUser" Min="0" Max="10" />
                                            <FieldHelp>Maximum number of auto-replies per user (0 = unlimited, 1-10 = limit)</FieldHelp>
                                        </Field>
                                    </Column>
                                </Row>
                                <Row>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Validation>
                                            <Field>
                                                <FieldLabel>End Date & Time</FieldLabel>
                                                <DateEdit TValue="DateTime?" @bind-Date="@Campaign.EndDate" InputMode="DateInputMode.DateTime">
                                                    <ValidationError />
                                                </DateEdit>
                                                <FieldHelp>Leave empty for no end date. Campaign will expire at the specified date and time.</FieldHelp>
                                            </Field>
                                        </Validation>
                                    </Column>
                                </Row>
                            </CardBody>
                        </Card>
                    </Column>
                </Row>

                <Row Class="mt-3">
                    <Column ColumnSize="ColumnSize.Is12">
                        <Card>
                            <CardHeader>
                                <h5>Reply Configuration</h5>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <Check @bind-Checked="@Campaign.SendPublicReply">Send Public Reply</Check>
                                            <FieldHelp>Reply publicly to comments (visible to everyone)</FieldHelp>
                                        </Field>
                                        @if (Campaign.SendPublicReply)
                                        {
                                            <!-- Public Reply Type Selector -->
                                            <Field>
                                                <FieldLabel>Public Reply Type *</FieldLabel>
                                                <RadioGroup @bind-CheckedValue="@Campaign.PublicReplyType" Orientation="Orientation.Horizontal">
                                                    <Radio Value="@PublicReplyType.Custom">Custom Reply</Radio>
                                                    <Radio Value="@PublicReplyType.Emoji">Emoji Reply</Radio>
                                                    <Radio Value="@PublicReplyType.Welcome">Welcome Reply</Radio>
                                                </RadioGroup>
                                                <FieldHelp>Choose how to respond to comments publicly</FieldHelp>
                                            </Field>

                                            @if (Campaign.PublicReplyType == PublicReplyType.Custom)
                                            {
                                                <!-- Custom Reply Message Editor -->
                                                <Validation>
                                                    <Field>
                                                        <FieldLabel>Public Reply Message *</FieldLabel>
                                                        <PersonalizationTagButtons OnTagSelected="@((tag) => InsertPersonalizationTag(tag, "publicReplyMessage"))" />
                                                        <div class="position-relative">
                                                            <MemoEdit @bind-Text="@Campaign.PublicReplyMessage" Rows="3" Placeholder="Enter your public reply message" Id="publicReplyMessage" />
                                                            <div class="position-absolute bottom-0 end-0 p-2">
                                                                <EmojiPicker OnEmojiSelected="@((emoji) => InsertEmoji(emoji, "publicReplyMessage"))" />
                                                            </div>
                                                        </div>
                                                        @if (!string.IsNullOrEmpty(Campaign.PublicReplyMessage) && Campaign.PublicReplyMessage.Length > 1000)
                                                        {
                                                            <Alert Color="Color.Danger" Visible="true">
                                                                <Icon Name="IconName.ExclamationTriangle" />
                                                                Public reply message cannot exceed 1000 characters.
                                                            </Alert>
                                                        }
                                                        <ValidationError />
                                                        <Alert Color="Color.Warning" Visible="true">
                                                            <Icon Name="IconName.ExclamationTriangle" />
                                                            <strong>Warning:</strong> Using a Custom Public Reply may result in your page being banned from commenting.
                                                        </Alert>
                                                        <FieldHelp>This message will be posted as a public comment reply</FieldHelp>
                                                    </Field>
                                                </Validation>
                                            }
                                            else
                                            {
                                                <!-- Preview for Emoji and Welcome Reply Types -->
                                                <Field>
                                                    <FieldLabel>Public Reply Preview</FieldLabel>
                                                    <div class="border rounded p-3 bg-light">
                                                        @if (Campaign.PublicReplyType == PublicReplyType.Emoji)
                                                        {
                                                            <div class="text-muted">
                                                                <Icon Name="IconName.Info" Class="me-1" />
                                                                Will generate exactly 3 random emojis (e.g., 😊🎉✨)
                                                            </div>
                                                        }
                                                        else if (Campaign.PublicReplyType == PublicReplyType.Welcome)
                                                        {
                                                            <div class="text-muted">
                                                                <Icon Name="IconName.Info" Class="me-1" />
                                                                Will generate: [Welcome Message] [First Name] [3 Random Emojis]<br />
                                                                <small>Example: "Welcome John 🎉✨🥳"</small>
                                                            </div>
                                                        }
                                                    </div>
                                                    <FieldHelp>Public Reply Text entry is only allowed for Custom Reply type</FieldHelp>
                                                </Field>
                                            }
                                        }
                                    </Column>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <Check Checked="@Campaign.SendPrivateReply" CheckedChanged="@(async (bool value) => await OnPrivateReplyToggled(value))">Send Private Message</Check>
                                            <FieldHelp>Send a private message to the commenter</FieldHelp>
                                        </Field>
                                        @if (Campaign.SendPrivateReply)
                                        {
                                            <!-- Reply Type Toggle -->
                                            <Field>
                                                <FieldLabel>Private Reply Type</FieldLabel>
                                                <RadioGroup CheckedValue="@Campaign.PrivateReplyType" TValue="PrivateReplyType" CheckedValueChanged="@(async (PrivateReplyType value) => await OnPrivateReplyTypeChanged(value))">
                                                    <Radio Value="PrivateReplyType.TextReply">Text Reply</Radio>
                                                    <Radio Value="PrivateReplyType.CardReply">Card Reply</Radio>
                                                </RadioGroup>
                                                <FieldHelp>Choose between a simple text message or an interactive card</FieldHelp>
                                            </Field>

                                            @if (Campaign.PrivateReplyType == PrivateReplyType.TextReply)
                                            {
                                                <!-- Text Reply Editor -->
                                                <Field>
                                                    <FieldLabel>Private Message</FieldLabel>
                                                    <PersonalizationTagButtons OnTagSelected="@((tag) => InsertPersonalizationTag(tag, "privateReplyMessage"))" />
                                                    <div class="position-relative">
                                                        <MemoEdit @bind-Text="@Campaign.PrivateReplyMessage" Rows="3" Placeholder="Enter your private message" Id="privateReplyMessage" />
                                                        <div class="position-absolute bottom-0 end-0 p-2">
                                                            <EmojiPicker Alignment="EmojiPicker.PickerAlignment.Right" OnEmojiSelected="@((emoji) => InsertEmoji(emoji, "privateReplyMessage"))" />
                                                        </div>
                                                    </div>
                                                    @if (!Campaign.IncludePostLinkInPrivateReply)
                                                    {
                                                        if (!string.IsNullOrEmpty(Campaign.PrivateReplyMessage) && Campaign.PrivateReplyMessage.Length > 2000)
                                                        {
                                                            <Alert Color="Color.Danger" Visible="true">
                                                                <Icon Name="IconName.ExclamationTriangle" />
                                                                Private reply message cannot exceed 2000 characters.
                                                            </Alert>
                                                        }
                                                    }
                                                    else
                                                    {
                                                        var hasPicture = SelectedPost != null && !string.IsNullOrEmpty(SelectedPost.PictureUrl);
                                                        if (hasPicture)
                                                        {
                                                            if (!string.IsNullOrEmpty(Campaign.PrivateReplyMessage) && Campaign.PrivateReplyMessage.Length > 500)
                                                            {
                                                                <Alert Color="Color.Danger" Visible="true">
                                                                    <Icon Name="IconName.ExclamationTriangle" />
                                                                    Private reply message cannot exceed 500 characters when using a template private reply.
                                                                </Alert>
                                                            }
                                                            if (!string.IsNullOrEmpty(Campaign.PrivateReplyMessage) && Campaign.PrivateReplyMessage.Length > 50)
                                                            {
                                                                <Alert Color="Color.Warning" Visible="true">
                                                                    <Icon Name="IconName.ExclamationTriangle" />
                                                                    When including the post link and image, the private message will be used as the template title and will be truncated to 50 characters.
                                                                </Alert>
                                                            }
                                                        }
                                                        else
                                                        {
                                                            if (!string.IsNullOrEmpty(Campaign.PrivateReplyMessage) && Campaign.PrivateReplyMessage.Length > 500)
                                                            {
                                                                <Alert Color="Color.Danger" Visible="true">
                                                                    <Icon Name="IconName.ExclamationTriangle" />
                                                                    Private reply message cannot exceed 500 characters when using a template private reply.
                                                                </Alert>
                                                            }
                                                        }
                                                    }
                                                    <FieldHelp>This message will be sent as a private message</FieldHelp>
                                                </Field>
                                            }
                                            else
                                            {
                                                <!-- Card Reply Editor -->
                                                <CardReplyEditor @bind-CardData="@Campaign.CardReplyData" OnValidationChanged="@OnCardReplyValidationChanged" />
                                            }
                                        }
                                    </Column>
                                </Row>

                                <Row>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <Check @bind-Checked="@Campaign.SendLike">Like User Comment</Check>
                                            <FieldHelp>Automatically like the user's comment</FieldHelp>
                                        </Field>
                                    </Column>
                                </Row>

                                <Row>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <Check @bind-Checked="@Campaign.IncludePostLinkInPrivateReply"
                                                   Disabled="@(Campaign.SendPrivateReply && Campaign.PrivateReplyType == PrivateReplyType.CardReply)">
                                                Include Post Link in Private Reply
                                            </Check>
                                            <FieldHelp>
                                                @if (Campaign.SendPrivateReply && Campaign.PrivateReplyType == PrivateReplyType.CardReply)
                                                {
                                                    <span class="text-muted">Not available with Card Reply (post link is automatically included as default action)</span>
                                                }
                                                else
                                                {
                                                    <span>If enabled, the private reply will include a button linking to the post.</span>
                                                }
                                            </FieldHelp>
                                        </Field>
                                    </Column>
                                </Row>

                                @if (!Campaign.SendPublicReply && !Campaign.SendPrivateReply && !Campaign.SendLike)
                                {
                                    <Alert Color="Color.Warning" Visible="true">
                                        <Icon Name="IconName.ExclamationTriangle" />
                                        Please select at least one interaction type (Public Reply, Private Message, or Like Comment).
                                    </Alert>
                                }
                            </CardBody>
                        </Card>
                    </Column>
                </Row>
            </Validations>

            <Row Class="mt-4">
                <Column ColumnSize="ColumnSize.Is12">
                    <Card>
                        <CardBody>
                            <div class="d-flex justify-content-end">
                                <Button Color="Color.Secondary" Class="me-2" @onclick="NavigateBack">
                                    <Icon Name="IconName.ArrowLeft" /> Cancel
                                </Button>
                                <Button Color="Color.Primary" Class="me-2" @onclick="SaveAsDraftAsync" Disabled="@(IsSaving || !CanSave)">
                                    <Icon Name="IconName.Save" /> Save as Draft
                                </Button>
                                <Button Color="Color.Success" @onclick="SaveAndActivateAsync" Disabled="@(IsSaving || !CanSave)">
                                    <Icon Name="IconName.Check" /> @(IsEditMode ? "Save and Activate" : "Create and Activate")
                                </Button>
                            </div>
                        </CardBody>
                    </Card>
                </Column>
            </Row>
        </div>
    }
</div>

@code {
    [Parameter]
    public Guid? Id { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public Guid? PostId { get; set; }

    private bool IsEditMode => Id.HasValue;
    private bool IsLoading = false;
    private bool IsSaving = false;
    private FacebookPostDto? SelectedPost;
    private CampaignFormModel Campaign = new();
    private Validations? ValidationsRef;
    private bool IsCardReplyValid = true;

    private bool CanSave =>
        !string.IsNullOrWhiteSpace(Campaign.CampaignName) &&
        (Campaign.SendPublicReply || Campaign.SendPrivateReply || Campaign.SendLike) &&
        (!Campaign.SendPublicReply || (!string.IsNullOrWhiteSpace(Campaign.PublicReplyMessage) && Campaign.PublicReplyMessage.Length <= 1000)) &&
        (!Campaign.SendPrivateReply || IsPrivateReplyValid());

    private bool IsPrivateReplyValid()
    {
        if (!Campaign.SendPrivateReply)
            return true;

        if (Campaign.PrivateReplyType == PrivateReplyType.CardReply)
        {
            return IsCardReplyValid && Campaign.CardReplyData.IsValid();
        }
        else
        {
            if (string.IsNullOrWhiteSpace(Campaign.PrivateReplyMessage))
                return false;
            if (!Campaign.IncludePostLinkInPrivateReply)
                return Campaign.PrivateReplyMessage.Length <= 2000;
            var hasPicture = SelectedPost != null && !string.IsNullOrEmpty(SelectedPost.PictureUrl);
            if (hasPicture)
                return Campaign.PrivateReplyMessage.Length <= 500;
            else
                return Campaign.PrivateReplyMessage.Length <= 500;
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            IsLoading = true;

            if (IsEditMode && Id.HasValue)
            {
                var existingCampaign = await CampaignService.GetAsync(Id.Value);
                if (existingCampaign.FacebookPostId != Guid.Empty)
                {
                    var post = await PostService.GetAsync(existingCampaign.FacebookPostId);
                    SelectedPost = post;
                }
                else
                {
                    SelectedPost = new FacebookPostDto
                    {
                        Id = Guid.Empty,
                        FacebookPostId = string.Empty,
                        Message = existingCampaign.PostMessage,
                        PageName = existingCampaign.PageName,
                        FacebookCreatedTime = DateTime.Now,
                        HasActiveCampaign = true,
                        PictureUrl = existingCampaign.PostPictureUrl                    };
                }

                Campaign = new CampaignFormModel
                {
                    CampaignName = existingCampaign.CampaignName,
                    Description = existingCampaign.Description,
                    PublicReplyMessage = existingCampaign.PublicReplyMessage,
                    PrivateReplyMessage = existingCampaign.PrivateReplyMessage,
                    SendPublicReply = existingCampaign.SendPublicReply,
                    SendPrivateReply = existingCampaign.SendPrivateReply,
                    SendLike = existingCampaign.SendLike,
                    EndDate = existingCampaign.EndDate,
                    MaxRepliesPerUser = existingCampaign.MaxRepliesPerUser,
                    IncludePostLinkInPrivateReply = existingCampaign.IncludePostLinkInPrivateReply,
                    PrivateReplyType = existingCampaign.PrivateReplyType,
                    CardReplyData = existingCampaign.CardReplyData ?? new CardReplyData()
                };
            }
            else if (PostId.HasValue)
            {
                SelectedPost = await PostService.GetAsync(PostId.Value);
                Campaign = new CampaignFormModel
                {
                    CampaignName = $"Auto-reply for {SelectedPost.PageName} post",
                    SendPublicReply = true,
                    SendPrivateReply = false,
                    MaxRepliesPerUser = 0,
                    IncludePostLinkInPrivateReply = false
                };
            }
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading data: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task SaveAsDraftAsync()
    {
        if (!await ValidateForm()) return;

        try
        {
            IsSaving = true;

            if (IsEditMode && Id.HasValue)
            {
                var updateDto = CreateUpdateDto();
                await CampaignService.UpdateAsync(Id.Value, updateDto);
                await Message.Success("Campaign saved successfully!");
            }
            else
            {
                var createDto = CreateCreateDto();
                await CampaignService.CreateAsync(createDto);
                await Message.Success("Campaign created successfully!");
            }

            NavigateBack();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error saving campaign: {ex.Message}");
        }
        finally
        {
            IsSaving = false;
        }
    }

    private async Task SaveAndActivateAsync()
    {
        if (!await ValidateForm()) return;

        try
        {
            IsSaving = true;

            AutoReplyCampaignDto campaign;

            if (IsEditMode && Id.HasValue)
            {
                var updateDto = CreateUpdateDto();
                campaign = await CampaignService.UpdateAsync(Id.Value, updateDto);
                campaign = await CampaignService.ActivateAsync(campaign.Id);
                await Message.Success("Campaign updated and activated successfully!");
            }
            else
            {
                var createDto = CreateCreateDto();
                campaign = await CampaignService.CreateAsync(createDto);
                campaign = await CampaignService.ActivateAsync(campaign.Id);
                await Message.Success("Campaign created and activated successfully!");
            }

            NavigationManager.NavigateTo($"/campaigns");
        }
        catch (Exception ex)
        {
            await Message.Error($"Error saving and activating campaign: {ex.Message}");
        }
        finally
        {
            IsSaving = false;
        }
    }

    private async Task<bool> ValidateForm()
    {
        if (ValidationsRef != null)
        {
            return await ValidationsRef.ValidateAll();
        }
        return false;
    }

    private CreateAutoReplyCampaignDto CreateCreateDto()
    {
        return new CreateAutoReplyCampaignDto
        {
            FacebookPostId = SelectedPost!.Id,
            CampaignName = Campaign.CampaignName,
            Description = Campaign.Description,
            PublicReplyMessage = Campaign.PublicReplyMessage,
            PrivateReplyMessage = Campaign.PrivateReplyMessage,
            SendPublicReply = Campaign.SendPublicReply,
            SendPrivateReply = Campaign.SendPrivateReply,
            SendLike = Campaign.SendLike,
            EndDate = Campaign.EndDate,
            MaxRepliesPerUser = Campaign.MaxRepliesPerUser,
            IncludePostLinkInPrivateReply = Campaign.IncludePostLinkInPrivateReply,
            PrivateReplyType = Campaign.PrivateReplyType,
            CardReplyData = Campaign.PrivateReplyType == PrivateReplyType.CardReply ? Campaign.CardReplyData : null
        };
    }

    private UpdateAutoReplyCampaignDto CreateUpdateDto()
    {
        return new UpdateAutoReplyCampaignDto
        {
            CampaignName = Campaign.CampaignName,
            Description = Campaign.Description,
            PublicReplyMessage = Campaign.PublicReplyMessage,
            PrivateReplyMessage = Campaign.PrivateReplyMessage,
            SendPublicReply = Campaign.SendPublicReply,
            SendPrivateReply = Campaign.SendPrivateReply,
            SendLike = Campaign.SendLike,
            EndDate = Campaign.EndDate,
            MaxRepliesPerUser = Campaign.MaxRepliesPerUser,
            IncludePostLinkInPrivateReply = Campaign.IncludePostLinkInPrivateReply,
            PrivateReplyType = Campaign.PrivateReplyType,
            CardReplyData = Campaign.PrivateReplyType == PrivateReplyType.CardReply ? Campaign.CardReplyData : null
        };
    }

    private void NavigateBack()
    {
        NavigationManager.NavigateTo("/campaigns");
    }

    private async Task InsertEmoji(string emoji, string textareaId)
    {
        await JSRuntime.InvokeVoidAsync("insertTextAtCursor", textareaId, emoji);
        if (textareaId == "publicReplyMessage")
        {
            Campaign.PublicReplyMessage = (Campaign.PublicReplyMessage ?? "") + emoji;
        }
        else if (textareaId == "privateReplyMessage")
        {
            Campaign.PrivateReplyMessage = (Campaign.PrivateReplyMessage ?? "") + emoji;
        }
        StateHasChanged();
    }

    private async Task InsertPersonalizationTag(string tag, string textareaId)
    {
        await JSRuntime.InvokeVoidAsync("insertTextAtCursor", textareaId, tag);
        if (textareaId == "publicReplyMessage")
        {
            // Update the bound property to reflect the change
            var textarea = await JSRuntime.InvokeAsync<string>("getTextareaValue", textareaId);
            Campaign.PublicReplyMessage = textarea;
        }
        else if (textareaId == "privateReplyMessage")
        {
            // Update the bound property to reflect the change
            var textarea = await JSRuntime.InvokeAsync<string>("getTextareaValue", textareaId);
            Campaign.PrivateReplyMessage = textarea;
        }
        StateHasChanged();
    }

    private async Task OpenFacebookPostAsync(string permalinkUrl)
    {
        await JSRuntime.InvokeVoidAsync("open", permalinkUrl, "_blank");
    }

    private async Task OnPrivateReplyToggled(bool isChecked)
    {
        Campaign.SendPrivateReply = isChecked;
        if (!isChecked)
        {
            // Reset private reply settings when disabled
            Campaign.PrivateReplyType = PrivateReplyType.TextReply;
            Campaign.PrivateReplyMessage = null;
            Campaign.CardReplyData = new CardReplyData();
            Campaign.IncludePostLinkInPrivateReply = false;
        }
        StateHasChanged();
    }

    private async Task OnPrivateReplyTypeChanged(PrivateReplyType newType)
    {
        Campaign.PrivateReplyType = newType;

        // When switching to Card Reply, disable "Include Post Link" option
        if (newType == PrivateReplyType.CardReply)
        {
            Campaign.IncludePostLinkInPrivateReply = false;
        }

        StateHasChanged();
    }

    private void OnCardReplyValidationChanged(bool isValid)
    {
        IsCardReplyValid = isValid;
        StateHasChanged();
    }

    public class CampaignFormModel
    {
        [Required]
        [StringLength(256)]
        public string CampaignName { get; set; } = "";

        public string? Description { get; set; }

        [Required]
        public string PublicReplyMessage { get; set; } = "";

        public string? PrivateReplyMessage { get; set; }

        public bool SendPublicReply { get; set; } = true;

        public PublicReplyType PublicReplyType { get; set; } = PublicReplyType.Custom;

        public bool SendPrivateReply { get; set; } = false;

        public bool SendLike { get; set; } = false;

        [FutureDate(ErrorMessage = "End date must be in the future")]
        public DateTime? EndDate { get; set; }

        [Range(0, 10)]
        public int MaxRepliesPerUser { get; set; } = 0;

        public bool IncludePostLinkInPrivateReply { get; set; } = false;

        // New properties for Card Reply
        public PrivateReplyType PrivateReplyType { get; set; } = PrivateReplyType.TextReply;
        public CardReplyData CardReplyData { get; set; } = new();
    }
}
