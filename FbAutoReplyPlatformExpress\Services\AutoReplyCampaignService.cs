using System;
using System.Linq;
using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Entities;
using FbAutoReplyPlatformExpress.Permissions;
using FbAutoReplyPlatformExpress.Services.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace FbAutoReplyPlatformExpress.Services;

[Authorize]
public class AutoReplyCampaignService : ApplicationService, IAutoReplyCampaignService
{
    private readonly IRepository<AutoReplyCampaign, Guid> _campaignRepository;
    private readonly IRepository<FacebookPost, Guid> _postRepository;
    private readonly IRepository<FacebookPage, Guid> _pageRepository;
    private readonly IRepository<FacebookUser, Guid> _facebookUserRepository;
    private readonly IRepository<CampaignActivity, Guid> _activityRepository;
    private readonly ILogger<AutoReplyCampaignService> _logger;

    public AutoReplyCampaignService(
        IRepository<AutoReplyCampaign, Guid> campaignRepository,
        IRepository<FacebookPost, Guid> postRepository,
        IRepository<FacebookPage, Guid> pageRepository,
        IRepository<FacebookUser, Guid> facebookUserRepository,
        IRepository<CampaignActivity, Guid> activityRepository,
        ILogger<AutoReplyCampaignService> logger)
    {
        _campaignRepository = campaignRepository;
        _postRepository = postRepository;
        _pageRepository = pageRepository;
        _facebookUserRepository = facebookUserRepository;
        _activityRepository = activityRepository;
        _logger = logger;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.View)]
    public async Task<PagedResultDto<AutoReplyCampaignDto>> GetListAsync(GetCampaignsInput input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        var queryable = await _campaignRepository.GetQueryableAsync();
        var postQueryable = await _postRepository.GetQueryableAsync();
        var pageQueryable = await _pageRepository.GetQueryableAsync();

        // Get campaigns that belong to the user (either through database posts or in-memory posts)
        var userPageIds = await AsyncExecuter.ToListAsync(
            pageQueryable.Where(p => p.FacebookUserId == facebookUser.Id).Select(p => p.FacebookPageId));

        var campaignQuery = queryable.Where(c =>
            // In-memory campaigns: check if page belongs to user
            (c.FacebookPostId == null && userPageIds.Contains(c.FacebookPageIdString)) ||
            // Database campaigns: check through post relationship
            (c.FacebookPostId != null && postQueryable.Any(p => p.Id == c.FacebookPostId &&
                pageQueryable.Any(pg => pg.Id == p.FacebookPageId && pg.FacebookUserId == facebookUser.Id))));

        var campaigns = await AsyncExecuter.ToListAsync(campaignQuery);

        // Apply filters
        if (input.FacebookPostId.HasValue)
        {
            campaigns = campaigns.Where(c => c.FacebookPostId == input.FacebookPostId.Value).ToList();
        }

        if (!string.IsNullOrEmpty(input.SearchText))
        {
            campaigns = campaigns.Where(c =>
                c.CampaignName.Contains(input.SearchText) ||
                c.PostContent.Contains(input.SearchText) ||
                c.PageName.Contains(input.SearchText)).ToList();
        }

        if (input.IsActive.HasValue)
        {
            campaigns = campaigns.Where(c => c.IsActive == input.IsActive.Value).ToList();
        }

        if (input.FromDate.HasValue)
        {
            campaigns = campaigns.Where(c => c.CreationTime >= input.FromDate.Value).ToList();
        }

        if (input.ToDate.HasValue)
        {
            campaigns = campaigns.Where(c => c.CreationTime <= input.ToDate.Value).ToList();
        }

        var totalCount = campaigns.Count;

        // Apply sorting
        if (!string.IsNullOrEmpty(input.Sorting))
        {
            // Simple sorting by creation time for now
            campaigns = input.Sorting.Contains("desc") ?
                campaigns.OrderByDescending(c => c.CreationTime).ToList() :
                campaigns.OrderBy(c => c.CreationTime).ToList();
        }
        else
        {
            campaigns = campaigns.OrderByDescending(c => c.CreationTime).ToList();
        }

        var pagedCampaigns = campaigns.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();

        var campaignDtos = new List<AutoReplyCampaignDto>();
        foreach (var campaign in pagedCampaigns)
        {
            var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);

            // Set post message and page name from stored metadata or database
            if (campaign.FacebookPostId.HasValue)
            {
                // Database post - get from post entity
                var post = await _postRepository.GetAsync(campaign.FacebookPostId.Value);
                var page = await _pageRepository.GetAsync(post.FacebookPageId);
                campaignDto.PostMessage = post.Message;
                campaignDto.PageName = page.PageName;
            }
            else
            {
                // In-memory post - use stored metadata
                campaignDto.PostMessage = campaign.PostContent;
                campaignDto.PageName = campaign.PageName;
            }

            campaignDtos.Add(campaignDto);
        }

        return new PagedResultDto<AutoReplyCampaignDto>(totalCount, campaignDtos);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.View)]
    public async Task<AutoReplyCampaignDto> GetAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var campaign = await _campaignRepository.GetAsync(id);

        var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);

        if (campaign.FacebookPostId.HasValue)
        {
            // Database post - get from post entity
            var post = await _postRepository.GetAsync(campaign.FacebookPostId.Value);
            var page = await _pageRepository.GetAsync(post.FacebookPageId);

            if (page.FacebookUserId != facebookUser.Id)
            {
                throw new UnauthorizedAccessException();
            }

            campaignDto.PostMessage = post.Message;
            campaignDto.PageName = page.PageName;
        }
        else
        {
            // In-memory post - validate page access and use stored metadata
            var pageQuery = await _pageRepository.GetQueryableAsync();
            var page = await AsyncExecuter.FirstOrDefaultAsync(
                pageQuery.Where(p => p.FacebookPageId == campaign.FacebookPageIdString && p.FacebookUserId == facebookUser.Id));

            if (page == null)
            {
                throw new UnauthorizedAccessException();
            }

            campaignDto.PostMessage = campaign.PostContent;
            campaignDto.PageName = campaign.PageName;
        }

        return campaignDto;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Create)]
    public async Task<AutoReplyCampaignDto> CreateAsync(CreateAutoReplyCampaignDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var post = await _postRepository.GetAsync(input.FacebookPostId);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        // Check if there's already an active campaign for this post
        var existingCampaign = await _campaignRepository.FirstOrDefaultAsync(c =>
            c.FacebookPostId == input.FacebookPostId && c.IsActive);

        if (existingCampaign != null)
        {
            throw new UserFriendlyException("There is already an active campaign for this post. Please deactivate it first.");
        }

        var campaign = new AutoReplyCampaign(
            GuidGenerator.Create(),
            input.FacebookPostId,
            input.CampaignName,
            input.PublicReplyMessage);

        if (!string.IsNullOrEmpty(input.Description))
        {
            campaign.Description = input.Description;
        }

        if (!string.IsNullOrEmpty(input.PrivateReplyMessage))
        {
            campaign.PrivateReplyMessage = input.PrivateReplyMessage;
        }

        campaign.ConfigureReplyTypes(input.SendPublicReply, input.SendPrivateReply, input.SendLike);
        campaign.PublicReplyType = input.PublicReplyType;
        campaign.SetEndDate(input.EndDate);
        campaign.SetMaxRepliesPerUser(input.MaxRepliesPerUser);

        // Validate public reply configuration
        if (input.SendPublicReply && input.PublicReplyType == Services.Dtos.PublicReplyType.Custom)
        {
            if (string.IsNullOrWhiteSpace(input.PublicReplyMessage))
            {
                throw new UserFriendlyException("Public reply message is required for Custom Reply type.");
            }
        }

        // Handle private reply configuration
        if (input.SendPrivateReply)
        {
            campaign.PrivateReplyType = input.PrivateReplyType;

            if (input.PrivateReplyType == Services.Dtos.PrivateReplyType.CardReply)
            {
                // Validate card reply data
                if (input.CardReplyData == null || !input.CardReplyData.IsValid())
                {
                    throw new UserFriendlyException("Invalid card reply configuration. Please ensure all required fields are provided.");
                }

                campaign.SetCardReplyData(input.CardReplyData);
                // Card replies automatically include post URL as default action, so disable the separate option
                campaign.IncludePostLinkInPrivateReply = false;
            }
            else
            {
                // Text reply
                if (string.IsNullOrWhiteSpace(input.PrivateReplyMessage))
                {
                    throw new UserFriendlyException("Private reply message is required for text replies.");
                }
                campaign.IncludePostLinkInPrivateReply = input.IncludePostLinkInPrivateReply;
            }
        }

        await _campaignRepository.InsertAsync(campaign);

        var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);
        campaignDto.PostMessage = post.Message;
        campaignDto.PageName = page.PageName;

        return campaignDto;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Create)]
    public async Task<AutoReplyCampaignDto> CreateFromPostAsync(CreateCampaignFromPostDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        // Validate that the user has access to the Facebook page
        var pageQuery = await _pageRepository.GetQueryableAsync();
        var page = await AsyncExecuter.FirstOrDefaultAsync(
            pageQuery.Where(p => p.FacebookPageId == input.FacebookPageId && p.FacebookUserId == facebookUser.Id));

        if (page == null)
        {
            throw new UnauthorizedAccessException("You don't have access to this Facebook page.");
        }

        // Check if there's already an active campaign for this post
        var campaignQuery = await _campaignRepository.GetQueryableAsync();
        var existingCampaign = await AsyncExecuter.FirstOrDefaultAsync(
            campaignQuery.Where(c => c.FacebookPostIdString == input.FacebookPostId && c.IsActive));

        if (existingCampaign != null)
        {
            throw new UserFriendlyException("There is already an active campaign for this post. Please deactivate it first.");
        }

        var campaign = new AutoReplyCampaign(
            GuidGenerator.Create(),
            input.FacebookPostId,
            input.FacebookPageId,
            input.PageName,
            input.PostContent,
            input.PostCreatedTime,
            input.CampaignName,
            input.PublicReplyMessage);

        if (!string.IsNullOrEmpty(input.Description))
        {
            campaign.Description = input.Description;
        }

        if (!string.IsNullOrEmpty(input.PrivateReplyMessage))
        {
            campaign.PrivateReplyMessage = input.PrivateReplyMessage;
        }

        campaign.ConfigureReplyTypes(input.SendPublicReply, input.SendPrivateReply, input.SendLike);
        campaign.PublicReplyType = input.PublicReplyType;
        campaign.SetEndDate(input.EndDate);
        campaign.SetMaxRepliesPerUser(input.MaxRepliesPerUser);
        campaign.UpdatePostMetadata(input.PostPermalinkUrl, input.PostPictureUrl);

        // Validate public reply configuration
        if (input.SendPublicReply && input.PublicReplyType == Services.Dtos.PublicReplyType.Custom)
        {
            if (string.IsNullOrWhiteSpace(input.PublicReplyMessage))
            {
                throw new UserFriendlyException("Public reply message is required for Custom Reply type.");
            }
        }

        // Handle private reply configuration
        if (input.SendPrivateReply)
        {
            campaign.PrivateReplyType = input.PrivateReplyType;

            if (input.PrivateReplyType == Services.Dtos.PrivateReplyType.CardReply)
            {
                // Validate card reply data
                if (input.CardReplyData == null || !input.CardReplyData.IsValid())
                {
                    throw new UserFriendlyException("Invalid card reply configuration. Please ensure all required fields are provided.");
                }

                campaign.SetCardReplyData(input.CardReplyData);
                // Card replies automatically include post URL as default action, so disable the separate option
                campaign.IncludePostLinkInPrivateReply = false;
            }
            else
            {
                // Text reply
                if (string.IsNullOrWhiteSpace(input.PrivateReplyMessage))
                {
                    throw new UserFriendlyException("Private reply message is required for text replies.");
                }
                campaign.IncludePostLinkInPrivateReply = input.IncludePostLinkInPrivateReply;
            }
        }

        await _campaignRepository.InsertAsync(campaign);

        var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);
        campaignDto.PostMessage = input.PostContent;
        campaignDto.PageName = input.PageName;

        return campaignDto;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Edit)]
    public async Task<AutoReplyCampaignDto> UpdateAsync(Guid id, UpdateAutoReplyCampaignDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var campaign = await _campaignRepository.GetAsync(id);

        // Validate user access to the campaign
        if (campaign.FacebookPostId.HasValue)
        {
            // Database post - check through post relationship
            var post = await _postRepository.GetAsync(campaign.FacebookPostId.Value);
            var page = await _pageRepository.GetAsync(post.FacebookPageId);

            if (page.FacebookUserId != facebookUser.Id)
            {
                throw new UnauthorizedAccessException();
            }
        }
        else
        {
            // In-memory post - check page access directly
            var pageQuery = await _pageRepository.GetQueryableAsync();
            var page = await AsyncExecuter.FirstOrDefaultAsync(
                pageQuery.Where(p => p.FacebookPageId == campaign.FacebookPageIdString && p.FacebookUserId == facebookUser.Id));

            if (page == null)
            {
                throw new UnauthorizedAccessException();
            }
        }

        if (!string.IsNullOrEmpty(input.CampaignName))
        {
            campaign.CampaignName = input.CampaignName;
        }

        if (input.Description != null)
        {
            campaign.Description = input.Description;
        }

        if (!string.IsNullOrEmpty(input.PublicReplyMessage) || !string.IsNullOrEmpty(input.PrivateReplyMessage))
        {
            campaign.UpdateMessages(
                input.PublicReplyMessage ?? campaign.PublicReplyMessage,
                input.PrivateReplyMessage);
        }

        if (input.SendPublicReply.HasValue || input.SendPrivateReply.HasValue || input.SendLike.HasValue)
        {
            campaign.ConfigureReplyTypes(
                input.SendPublicReply ?? campaign.SendPublicReply,
                input.SendPrivateReply ?? campaign.SendPrivateReply,
                input.SendLike ?? campaign.SendLike);
        }

        if (input.EndDate.HasValue)
        {
            campaign.SetEndDate(input.EndDate);
        }

        if (input.MaxRepliesPerUser.HasValue)
        {
            campaign.SetMaxRepliesPerUser(input.MaxRepliesPerUser.Value);
        }

        // Handle public reply type updates
        if (input.PublicReplyType.HasValue)
        {
            campaign.PublicReplyType = input.PublicReplyType.Value;

            // Validate public reply configuration
            if (campaign.SendPublicReply && input.PublicReplyType.Value == Services.Dtos.PublicReplyType.Custom)
            {
                if (string.IsNullOrWhiteSpace(campaign.PublicReplyMessage))
                {
                    throw new UserFriendlyException("Public reply message is required for Custom Reply type.");
                }
            }
        }

        // Handle private reply type and card reply data updates
        if (input.PrivateReplyType.HasValue)
        {
            campaign.PrivateReplyType = input.PrivateReplyType.Value;

            if (input.PrivateReplyType.Value == Services.Dtos.PrivateReplyType.CardReply)
            {
                // Validate card reply data
                if (input.CardReplyData == null || !input.CardReplyData.IsValid())
                {
                    throw new UserFriendlyException("Invalid card reply configuration. Please ensure all required fields are provided.");
                }

                campaign.SetCardReplyData(input.CardReplyData);
                // Card replies automatically include post URL as default action, so disable the separate option
                campaign.IncludePostLinkInPrivateReply = false;
            }
            else
            {
                // Text reply - clear card data
                campaign.SetCardReplyData(null);

                // Handle include post link option for text replies
                if (input.IncludePostLinkInPrivateReply.HasValue)
                {
                    campaign.IncludePostLinkInPrivateReply = input.IncludePostLinkInPrivateReply.Value;
                }
            }
        }
        else if (input.IncludePostLinkInPrivateReply.HasValue && campaign.PrivateReplyType == Services.Dtos.PrivateReplyType.TextReply)
        {
            // Only update include post link if it's a text reply
            campaign.IncludePostLinkInPrivateReply = input.IncludePostLinkInPrivateReply.Value;
        }

        await _campaignRepository.UpdateAsync(campaign);

        return await GetAsync(id);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Delete)]
    public async Task DeleteAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var campaign = await _campaignRepository.GetAsync(id);

        // Validate user access to the campaign
        if (campaign.FacebookPostId.HasValue)
        {
            // Database post - check through post relationship
            var post = await _postRepository.GetAsync(campaign.FacebookPostId.Value);
            var page = await _pageRepository.GetAsync(post.FacebookPageId);

            if (page.FacebookUserId != facebookUser.Id)
            {
                throw new UnauthorizedAccessException();
            }
        }
        else
        {
            // In-memory post - check page access directly
            var pageQuery = await _pageRepository.GetQueryableAsync();
            var page = await AsyncExecuter.FirstOrDefaultAsync(
                pageQuery.Where(p => p.FacebookPageId == campaign.FacebookPageIdString && p.FacebookUserId == facebookUser.Id));

            if (page == null)
            {
                throw new UnauthorizedAccessException();
            }
        }

        await _campaignRepository.DeleteAsync(campaign);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Activate)]
    public async Task<AutoReplyCampaignDto> ActivateAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var campaign = await _campaignRepository.GetAsync(id);

        // Validate user access to the campaign
        if (campaign.FacebookPostId.HasValue)
        {
            // Database post - check through post relationship
            var post = await _postRepository.GetAsync(campaign.FacebookPostId.Value);
            var page = await _pageRepository.GetAsync(post.FacebookPageId);

            if (page.FacebookUserId != facebookUser.Id)
            {
                throw new UnauthorizedAccessException();
            }
        }
        else
        {
            // In-memory post - check page access directly
            var pageQuery = await _pageRepository.GetQueryableAsync();
            var page = await AsyncExecuter.FirstOrDefaultAsync(
                pageQuery.Where(p => p.FacebookPageId == campaign.FacebookPageIdString && p.FacebookUserId == facebookUser.Id));

            if (page == null)
            {
                throw new UnauthorizedAccessException();
            }
        }

        // Check if there's already an active campaign for this post
        var existingActiveCampaign = await _campaignRepository.FirstOrDefaultAsync(c =>
            c.FacebookPostIdString == campaign.FacebookPostIdString && c.IsActive && c.Id != id);

        if (existingActiveCampaign != null)
        {
            throw new UserFriendlyException("There is already an active campaign for this post. Please deactivate it first.");
        }

        campaign.Activate();
        await _campaignRepository.UpdateAsync(campaign);

        return await GetAsync(id);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Deactivate)]
    public async Task<AutoReplyCampaignDto> DeactivateAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var campaign = await _campaignRepository.GetAsync(id);

        // Validate user access to the campaign
        if (campaign.FacebookPostId.HasValue)
        {
            // Database post - check through post relationship
            var post = await _postRepository.GetAsync(campaign.FacebookPostId.Value);
            var page = await _pageRepository.GetAsync(post.FacebookPageId);

            if (page.FacebookUserId != facebookUser.Id)
            {
                throw new UnauthorizedAccessException();
            }
        }
        else
        {
            // In-memory post - check page access directly
            var pageQuery = await _pageRepository.GetQueryableAsync();
            var page = await AsyncExecuter.FirstOrDefaultAsync(
                pageQuery.Where(p => p.FacebookPageId == campaign.FacebookPageIdString && p.FacebookUserId == facebookUser.Id));

            if (page == null)
            {
                throw new UnauthorizedAccessException();
            }
        }

        campaign.Deactivate();
        await _campaignRepository.UpdateAsync(campaign);

        return await GetAsync(id);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.ViewActivities)]
    public async Task<PagedResultDto<CampaignActivityDto>> GetActivitiesAsync(GetActivitiesInput input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        var queryable = await _activityRepository.GetQueryableAsync();
        var campaignQueryable = await _campaignRepository.GetQueryableAsync();
        var postQueryable = await _postRepository.GetQueryableAsync();
        var pageQueryable = await _pageRepository.GetQueryableAsync();

        // Handle both in-memory campaigns and database post campaigns
        var userPageIds = await AsyncExecuter.ToListAsync(
            pageQueryable.Where(p => p.FacebookUserId == facebookUser.Id).Select(p => p.FacebookPageId));

        var userCampaignIds = await AsyncExecuter.ToListAsync(
            campaignQueryable.Where(c =>
                // In-memory campaigns: check if page belongs to user
                (c.FacebookPostId == null && userPageIds.Contains(c.FacebookPageIdString)) ||
                // Database campaigns: check through post relationship
                (c.FacebookPostId != null && postQueryable.Any(p => p.Id == c.FacebookPostId &&
                    pageQueryable.Any(pg => pg.Id == p.FacebookPageId && pg.FacebookUserId == facebookUser.Id))))
            .Select(c => c.Id));

        var query = from activity in queryable
                    where userCampaignIds.Contains(activity.CampaignId)
                    select activity;

        // Apply filters
        if (input.CampaignId.HasValue)
        {
            query = query.Where(x => x.CampaignId == input.CampaignId.Value);
        }

        if (!string.IsNullOrEmpty(input.SearchText))
        {
            query = query.Where(x => x.CommenterName.Contains(input.SearchText) ||
                                   x.OriginalComment.Contains(input.SearchText));
        }

        if (input.HasError.HasValue)
        {
            query = query.Where(x => x.HasError == input.HasError.Value);
        }

        if (input.PublicReplySent.HasValue)
        {
            query = query.Where(x => x.PublicReplySent == input.PublicReplySent.Value);
        }

        if (input.PrivateReplySent.HasValue)
        {
            query = query.Where(x => x.PrivateReplySent == input.PrivateReplySent.Value);
        }

        if (input.FromDate.HasValue)
        {
            query = query.Where(x => x.CommentCreatedAt >= input.FromDate.Value);
        }

        if (input.ToDate.HasValue)
        {
            query = query.Where(x => x.CommentCreatedAt <= input.ToDate.Value);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply sorting
        if (!string.IsNullOrEmpty(input.Sorting))
        {
            // For now, use default sorting. Dynamic sorting would require System.Linq.Dynamic.Core
            query = query.OrderByDescending(x => x.CommentCreatedAt);
        }
        else
        {
            query = query.OrderByDescending(x => x.CommentCreatedAt);
        }

        query = query.Skip(input.SkipCount).Take(input.MaxResultCount);

        var activities = await AsyncExecuter.ToListAsync(query);
        var activityDtos = new List<CampaignActivityDto>();

        foreach (var activity in activities)
        {
            var activityDto = ObjectMapper.Map<CampaignActivity, CampaignActivityDto>(activity);

            // Get campaign details
            var campaign = await _campaignRepository.GetAsync(activity.CampaignId);
            activityDto.CampaignName = campaign.CampaignName;

            // Set post message and page name from stored metadata or database
            if (campaign.FacebookPostId.HasValue)
            {
                // Database post - get from post entity
                var post = await _postRepository.GetAsync(campaign.FacebookPostId.Value);
                var page = await _pageRepository.GetAsync(post.FacebookPageId);
                activityDto.PostMessage = post.Message;
                activityDto.PageName = page.PageName;
            }
            else
            {
                // In-memory post - use stored metadata
                activityDto.PostMessage = campaign.PostContent;
                activityDto.PageName = campaign.PageName;
            }

            activityDtos.Add(activityDto);
        }

        return new PagedResultDto<CampaignActivityDto>(totalCount, activityDtos);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.ViewActivities)]
    public async Task<CampaignActivityDto> GetActivityAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var activity = await _activityRepository.GetAsync(id);
        var campaign = await _campaignRepository.GetAsync(activity.CampaignId);

        var activityDto = ObjectMapper.Map<CampaignActivity, CampaignActivityDto>(activity);
        activityDto.CampaignName = campaign.CampaignName;

        if (campaign.FacebookPostId.HasValue)
        {
            // Database post - get from post entity
            var post = await _postRepository.GetAsync(campaign.FacebookPostId.Value);
            var page = await _pageRepository.GetAsync(post.FacebookPageId);

            if (page.FacebookUserId != facebookUser.Id)
            {
                throw new UnauthorizedAccessException();
            }

            activityDto.PostMessage = post.Message;
            activityDto.PageName = page.PageName;
        }
        else
        {
            // In-memory post - use stored metadata and validate page access
            var pageQuery = await _pageRepository.GetQueryableAsync();
            var page = await AsyncExecuter.FirstOrDefaultAsync(
                pageQuery.Where(p => p.FacebookPageId == campaign.FacebookPageIdString && p.FacebookUserId == facebookUser.Id));

            if (page == null)
            {
                throw new UnauthorizedAccessException();
            }

            activityDto.PostMessage = campaign.PostContent;
            activityDto.PageName = campaign.PageName;
        }

        return activityDto;
    }

    public async Task<AutoReplyCampaignDto?> GetActiveCampaignForPostAsync(string facebookPostId)
    {
        // First try to find campaign by FacebookPostIdString (for in-memory posts)
        var campaign = await _campaignRepository.FirstOrDefaultAsync(c =>
            c.FacebookPostIdString == facebookPostId);

        if (campaign != null)
        {
            // For in-memory posts, use the stored metadata
            var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);
            campaignDto.PostMessage = campaign.PostContent;
            campaignDto.PageName = campaign.PageName;
            return campaignDto;
        }

        //// Fallback: try to find campaign by database post (for backward compatibility)
        //var post = await _postRepository.FirstOrDefaultAsync(p => p.FacebookPostId == facebookPostId);
        //if (post != null)
        //{
        //    campaign = await _campaignRepository.FirstOrDefaultAsync(c =>
        //        c.FacebookPostId == post.Id && c.IsActive && c.IsValidForReply());

        //    if (campaign != null)
        //    {
        //        var page = await _pageRepository.GetAsync(post.FacebookPageId);
        //        var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);
        //        campaignDto.PostMessage = post.Message;
        //        campaignDto.PageName = page.PageName;
        //        return campaignDto;
        //    }
        //}

        return null;
    }

    private async Task<FacebookUser> GetCurrentUserFacebookUserAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);

        if (facebookUser == null)
        {
            throw new UserFriendlyException("Facebook account not connected. Please connect your Facebook account first.");
        }

        return facebookUser;
    }
}
